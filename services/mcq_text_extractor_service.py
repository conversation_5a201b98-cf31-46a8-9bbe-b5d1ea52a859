"""
MCQ Text Extractor Service

This service handles the extraction of text content from MCQ images.
It converts PDFs to images, extracts text from each image using LLM, and saves the result.
"""

import logging
import os
import asyncio
import re
import traceback
import uuid
import json
import math
import gc
from typing import Dict, List, Any, Optional

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.mcq_extractor import MCQExtractor
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, mcq_parser_prompt
from utils.s3_utils import upload_file_to_s3
from sqlalchemy import text
from db_config.db import get_session, CONTENT_SCHEMA
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage

# Import memory management functions from MCQ extractor
from agents.mcq_extractor import (
    get_memory_usage,
    get_system_memory_info,
    force_garbage_collection,
    complete_memory_cleanup,
    check_memory_limit
)

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class MCQTextExtractorService:
    """
    Service for extracting text content from MCQ images.
    """

    def __init__(self, max_concurrent_extractions: int = None):
        """
        Initialize the MCQTextExtractorService.

        Args:
            max_concurrent_extractions: Maximum number of concurrent extractions to run in parallel
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 3)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1)
        logger.info(f"MCQTextExtractorService initialized with max_concurrent_extractions={self.max_concurrent_extractions}")

    def monitor_memory_during_processing(self, context: str = "", force_cleanup_threshold: float = 0.85):
        """Monitor memory during processing and perform cleanup if needed"""
        try:
            current_memory = get_memory_usage()
            system_memory = get_system_memory_info()

            # Get thresholds from config
            max_memory = getattr(config, 'MAX_MEMORY_MB', 1024)
            critical_memory = getattr(config, 'CRITICAL_MEMORY_MB', 1280)

            # Calculate memory usage percentages
            memory_usage_percent = current_memory / max_memory

            logger.info(f"🔍 Memory monitor {context} - Process: {current_memory:.2f}MB ({memory_usage_percent:.1%} of limit), "
                       f"System: {system_memory['percent']:.1f}%")

            # Check if we need to perform cleanup
            if memory_usage_percent >= force_cleanup_threshold:
                logger.warning(f"Memory usage ({current_memory:.2f}MB) above cleanup threshold ({force_cleanup_threshold:.1%} of {max_memory}MB)")
                logger.info("Performing preventive memory cleanup...")

                # Perform aggressive garbage collection
                collected = force_garbage_collection(aggressive=True)

                # Check memory after cleanup
                new_memory = get_memory_usage()
                memory_freed = current_memory - new_memory

                logger.info(f"✅ Preventive cleanup complete - Memory: {current_memory:.2f}MB → {new_memory:.2f}MB "
                           f"(freed {memory_freed:.2f}MB), Objects: {collected}")

                return {
                    'cleanup_performed': True,
                    'memory_before': current_memory,
                    'memory_after': new_memory,
                    'memory_freed': memory_freed,
                    'objects_collected': collected
                }

            # Check for critical memory levels
            if current_memory >= critical_memory:
                logger.error(f"🚨 CRITICAL MEMORY LEVEL: {current_memory:.2f}MB >= {critical_memory}MB")
                raise MemoryError(f"Critical memory usage: {current_memory:.2f}MB")

            return {
                'cleanup_performed': False,
                'current_memory': current_memory,
                'memory_usage_percent': memory_usage_percent,
                'system_percent': system_memory['percent']
            }

        except MemoryError:
            raise
        except Exception as e:
            logger.error(f"Error during memory monitoring: {e}")
            return {'error': str(e)}

    def log_memory_status(self, context: str = ""):
        """Log current memory status with context"""
        try:
            process_memory = get_memory_usage()
            system_memory = get_system_memory_info()
            logger.info(f"📊 Memory status {context} - Process: {process_memory:.2f} MB, "
                       f"System: {system_memory['percent']:.1f}% used, Available: {system_memory['available']:.2f} MB")
        except Exception as e:
            logger.warning(f"Could not log memory status: {e}")

    async def extract_text_from_resource(self, res_id: str, username: str = None, force_reextract: bool = False, total_questions: int = None) -> Dict:
        """
        Extract text content from a PDF resource.

        Args:
            res_id: Resource ID
            username: Username of the user performing the extraction
            force_reextract: Whether to force re-extraction even if file exists
            total_questions: Total number of questions for MCQ parsing batches

        Returns:
            Dict: Result of the extraction process
        """
        request_id = str(uuid.uuid4())
        logger.info(f"[REQUEST:{request_id}] Starting MCQ text extraction for resource ID {res_id}")

        # Store initial memory state for tracking
        initial_memory_state = {
            'process_memory': get_memory_usage(),
            'system_memory': get_system_memory_info()
        }

        try:
            # Enhanced initial memory check
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            initial_memory = check_memory_limit(memory_limit, critical_check=True)
            logger.info(f"🚀 [REQUEST:{request_id}] Starting MCQ text extraction - Initial memory: Process={initial_memory:.2f}MB, "
                       f"System={initial_memory_state['system_memory']['percent']:.1f}% (limit: {memory_limit}MB)")
            # Step 1: Get resource details using MCQExtractor's method
            mcq_extractor = MCQExtractor()
            resource_details = await mcq_extractor.get_resource_details(res_id)

            if resource_details["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] Error getting resource details: {resource_details['message']}")
                return resource_details

            resource_id = resource_details["resource_id"]
            chapter_id = resource_details["chapter_id"]
            book_id = resource_details["book_id"]
            file_path = resource_details["file_path"]

            logger.info(f"[REQUEST:{request_id}] Resource details - ID: {resource_id}, Chapter: {chapter_id}, Book: {book_id}")

            # Step 1.5: Check if text file already exists in S3 (unless force_reextract is True)
            if not force_reextract:
                existing_text_result = await self.get_extracted_text(chapter_id, res_id)
                if existing_text_result["status"] == "success":
                    logger.info(f"[REQUEST:{request_id}] Text file already exists, returning existing content")
                    return {
                        "status": "success",
                        "message": "Text file already exists, no extraction needed",
                        "s3_path": existing_text_result["s3_path"],
                        "total_images": 0,
                        "text_files_created": 0,
                        "chapter_id": chapter_id,
                        "resource_id": resource_id,
                        "already_existed": True
                    }
            else:
                logger.info(f"[REQUEST:{request_id}] Force re-extraction requested, proceeding with extraction")

            # Step 2: Convert PDF to column images with memory monitoring
            logger.info(f"[REQUEST:{request_id}] Starting PDF to image conversion")
            check_memory_limit(memory_limit, critical_check=True)  # Critical check before PDF conversion

            pdf_converter = PDFImageConverter()
            conversion_result = pdf_converter.convert_and_upload(
                pdf_path=file_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                zoom=self.pdf_zoom_factor
            )

            if conversion_result["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] PDF conversion failed: {conversion_result['message']}")
                return {"status": "error", "message": conversion_result["message"]}

            # Get the column image URLs
            col_img_urls = conversion_result.get("cropped_image_urls", [])
            if not col_img_urls:
                logger.error(f"[REQUEST:{request_id}] No column images found")
                return {"status": "error", "message": "No column images found"}

            # Force cleanup after PDF conversion
            force_garbage_collection()
            self.log_memory_status("after PDF conversion")
            logger.info(f"[REQUEST:{request_id}] Found {len(col_img_urls)} column images")

            # Step 3: Extract text from images in parallel
            text_files = await self._extract_text_parallel(col_img_urls, request_id, resource_id, chapter_id, book_id)

            if not text_files:
                logger.error(f"[REQUEST:{request_id}] No text files were created")
                return {"status": "error", "message": "No text files were created"}

            # Force cleanup after text extraction
            force_garbage_collection()
            self.log_memory_status("after text extraction")

            # Step 4: Merge all text files into one combined file
            combined_file_path = await self._merge_text_files(text_files, chapter_id, resource_id, request_id)

            if not combined_file_path:
                logger.error(f"[REQUEST:{request_id}] Failed to create combined text file")
                return {"status": "error", "message": "Failed to create combined text file"}

            # Step 5: Upload combined file to S3
            s3_upload_result = upload_file_to_s3(
                local_file_path=combined_file_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=f"{chapter_id}_{resource_id}.txt",
                is_quiz_image=False
            )

            if not s3_upload_result:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined file to S3")
                return {"status": "error", "message": "Failed to upload combined file to S3"}

            # Force cleanup after S3 upload
            force_garbage_collection()
            self.log_memory_status("after S3 upload")

            images_from_content = self.get_quiz_images(res_id, chapter_id, book_id, conversion_result.get("image_urls", []))
            print(images_from_content)
            # Step 6: MCQ parsing and processing
            json_s3_path = None
            if total_questions:
                logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing for {total_questions} questions")

                # Read the combined text file content from S3
                from utils.s3_utils import read_file_from_s3, get_s3_path

                try:
                    # Get the S3 path for the uploaded text file
                    full_s3_path = get_s3_path(s3_upload_result)
                    logger.info(f"[REQUEST:{request_id}] Reading combined text from S3: {full_s3_path}")

                    # Read content from S3
                    content = read_file_from_s3(full_s3_path)
                    if content is not None:
                        # Convert bytes to string
                        combined_text_content = content.decode("utf-8")
                        logger.info(f"[REQUEST:{request_id}] Successfully read {len(combined_text_content)} characters from S3")

                        # Process MCQ parsing in batches
                        json_s3_path = await self._process_mcq_parsing_batches(
                            combined_text_content, total_questions, resource_id, chapter_id,
                            book_id, request_id, combined_file_path, username, images_from_content
                        )

                        if json_s3_path:
                            logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully, JSON uploaded to: {json_s3_path}")
                        else:
                            logger.warning(f"[REQUEST:{request_id}] MCQ parsing failed or JSON upload failed")
                    else:
                        logger.error(f"[REQUEST:{request_id}] Failed to read combined text file from S3: {full_s3_path}")
                        logger.warning(f"[REQUEST:{request_id}] Skipping MCQ parsing due to S3 read failure")

                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error reading from S3 for MCQ parsing: {e}")
                    logger.warning(f"[REQUEST:{request_id}] Skipping MCQ parsing due to S3 error")
            else:
                logger.info(f"[REQUEST:{request_id}] Skipping MCQ parsing as total_questions not provided")

            # Step 7: Clean up local files
            self._cleanup_local_files(text_files + [combined_file_path])

            logger.info(f"[REQUEST:{request_id}] MCQ text extraction completed successfully")
            from agents.task_manager import delete_folder_by_id

            if delete_folder_by_id(book_id, chapter_id, res_id):
                logger.info(f"Successfully deleted resource folder for res_id: {res_id}")
            else:
                logger.warning(f"Failed to delete resource folder for res_id: {res_id}")

            # Prepare return response
            response = {
                "status": "success",
                "message": f"Successfully extracted text from {len(col_img_urls)} images",
                "s3_path": s3_upload_result,
                "total_images": len(col_img_urls),
                "text_files_created": len(text_files),
                "chapter_id": chapter_id,
                "resource_id": resource_id
            }

            # Add JSON processing information if it was performed
            if total_questions:
                response["mcq_parsing"] = {
                    "total_questions": total_questions,
                    "json_s3_path": json_s3_path,
                    "parsing_completed": json_s3_path is not None,
                    "api_processing_completed": json_s3_path is not None  # API processing happens if JSON was created successfully
                }

            return response

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error extracting text: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

        finally:
            # Comprehensive final cleanup regardless of success or failure
            try:
                logger.info(f"🧹 [REQUEST:{request_id}] Starting final comprehensive cleanup...")

                # Perform complete memory cleanup
                cleanup_stats = complete_memory_cleanup()

                # Get final memory state
                final_memory_state = {
                    'process_memory': get_memory_usage(),
                    'system_memory': get_system_memory_info()
                }

                # Calculate memory recovery
                process_memory_change = final_memory_state['process_memory'] - initial_memory_state['process_memory']
                system_memory_change = final_memory_state['system_memory']['percent'] - initial_memory_state['system_memory']['percent']

                logger.info(f"🏁 [REQUEST:{request_id}] Final cleanup complete - "
                           f"Process memory: {initial_memory_state['process_memory']:.2f}MB → {final_memory_state['process_memory']:.2f}MB "
                           f"(change: {process_memory_change:+.2f}MB), "
                           f"System memory: {initial_memory_state['system_memory']['percent']:.1f}% → {final_memory_state['system_memory']['percent']:.1f}% "
                           f"(change: {system_memory_change:+.1f}%)")

                if cleanup_stats and cleanup_stats.get('cleanup_effective'):
                    logger.info(f"✅ [REQUEST:{request_id}] Memory cleanup was effective - "
                               f"freed {cleanup_stats.get('memory_freed_mb', 0):.2f}MB, "
                               f"collected {cleanup_stats.get('objects_collected', 0)} objects")

            except Exception as cleanup_error:
                logger.error(f"[REQUEST:{request_id}] Error during final cleanup: {cleanup_error}")

    async def _extract_text_parallel(self, col_img_urls: List[str], request_id: str, resource_id: str, chapter_id: str, book_id: str) -> List[str]:
        """
        Extract text from images in parallel using ThreadPoolExecutor for true parallelism.

        Args:
            col_img_urls: List of column image URLs
            request_id: Request ID for logging
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID

        Returns:
            List[str]: List of created text file paths
        """
        # Create output directory for text files
        output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "text_extraction")
        os.makedirs(output_dir, exist_ok=True)

        def process_single_image_sync(args):
            """
            Synchronous function to process a single image in a thread.

            Args:
                args: Tuple of (index, image_url)

            Returns:
                str or None: Path to created text file, or None if failed
            """
            i, image_url = args
            try:
                # Parse page and column numbers from image URL
                page_number, col_number = self._parse_image_info(image_url)

                logger.info(f"[REQUEST:{request_id}] Extracting text from page {page_number}, column {col_number}")

                # Create extractor instance for this thread
                extractor = ExtractorAgent()

                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Run the extraction with a timeout
                    async def run_with_timeout():
                        return await asyncio.wait_for(
                            extractor._extract_with_prompt([image_url], mcq_text_extractor_prompt(), md_parse=False, md_result=False),
                            timeout=getattr(config, 'EXTRACTION_TIMEOUT_SECONDS', 900)
                        )

                    # Run the extraction in the current thread (blocking operation)
                    result, _ = loop.run_until_complete(run_with_timeout())

                    # Create text file name
                    text_file_name = f"page_{page_number}_col_{col_number}.txt"
                    text_file_path = os.path.join(output_dir, text_file_name)

                    # Save the extracted text to file
                    with open(text_file_path, "w", encoding="utf-8") as f:
                        f.write(result)

                    logger.info(f"[REQUEST:{request_id}] Successfully extracted text to {text_file_name}")
                    return text_file_path

                finally:
                    loop.close()

            except Exception as e:
                logger.error(f"[REQUEST:{request_id}] Error extracting text from page {page_number}, column {col_number}: {e}")
                return None

        # Create a list of arguments for each task
        task_args = [(i, image_url) for i, image_url in enumerate(col_img_urls)]

        logger.info(f"[REQUEST:{request_id}] Starting parallel text extraction for {len(task_args)} images using ThreadPoolExecutor")

        # Execute tasks in parallel using ThreadPoolExecutor
        from concurrent.futures import ThreadPoolExecutor, as_completed

        text_files = []
        failed_count = 0

        with ThreadPoolExecutor(max_workers=self.max_concurrent_extractions) as executor:
            # Submit all tasks and get future objects
            futures = []
            for arg in task_args:
                future = executor.submit(process_single_image_sync, arg)
                futures.append((future, arg))

            # Process results as they complete
            for future, arg in futures:
                try:
                    # Get the result from the future with a reasonable timeout
                    timeout_seconds = getattr(config, 'FUTURE_TIMEOUT_SECONDS', 1200)
                    result = future.result(timeout=timeout_seconds)

                    if result is not None:
                        text_files.append(result)
                    else:
                        failed_count += 1

                except Exception as exc:
                    logger.error(f"[REQUEST:{request_id}] Task for image {arg[0]+1} generated an exception: {exc}")
                    failed_count += 1

        logger.info(f"[REQUEST:{request_id}] Completed parallel text extraction. Created {len(text_files)} text files, {failed_count} failed")
        return text_files

    def _parse_image_info(self, image_url: str) -> tuple:
        """
        Parse page and column numbers from image URL.

        Args:
            image_url: Image URL containing page and column info

        Returns:
            tuple: (page_number, col_number)
        """
        try:
            # Extract filename from URL
            filename = os.path.basename(image_url)
            # Expected format: page_X_col_Y.png
            parts = filename.replace('.png', '').split('_')
            page_number = int(parts[1])
            col_number = int(parts[3])
            return page_number, col_number
        except Exception as e:
            logger.warning(f"Could not parse image info from {image_url}: {e}")
            return 1, 1  # Default values

    async def _merge_text_files(self, text_files: List[str], chapter_id: str, resource_id: str, request_id: str) -> Optional[str]:
        """
        Merge all text files into one combined file.

        Args:
            text_files: List of text file paths
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: Path to the combined file, or None if failed
        """
        try:
            # Sort text files by page and column number
            sorted_files = sorted(text_files, key=lambda x: self._get_sort_key(x))
            
            # Create combined file path
            combined_file_name = f"{chapter_id}_{resource_id}.txt"
            combined_file_path = os.path.join(os.path.dirname(text_files[0]), combined_file_name)
            
            logger.info(f"[REQUEST:{request_id}] Merging {len(sorted_files)} text files into {combined_file_name}")
            
            # Merge all text files
            with open(combined_file_path, "w", encoding="utf-8") as combined_file:
                for i, text_file_path in enumerate(sorted_files):
                    with open(text_file_path, "r", encoding="utf-8") as f:
                        content = f.read().strip()
                        if content:
                            if i > 0:
                                combined_file.write("\n\n")
                            combined_file.write(content)
            
            logger.info(f"[REQUEST:{request_id}] Successfully created combined file: {combined_file_name}")
            return combined_file_path
            
        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error merging text files: {e}")
            return None

    def _get_sort_key(self, file_path: str) -> tuple:
        """
        Get sort key for text file based on page and column numbers.

        Args:
            file_path: Path to text file

        Returns:
            tuple: (page_number, col_number) for sorting
        """
        try:
            filename = os.path.basename(file_path)
            # Expected format: page_X_col_Y.txt
            parts = filename.replace('.txt', '').split('_')
            page_number = int(parts[1])
            col_number = int(parts[3])
            return page_number, col_number
        except Exception:
            return 999, 999  # Put unparseable files at the end

    def _cleanup_local_files(self, file_paths: List[str]):
        """
        Clean up local files.

        Args:
            file_paths: List of file paths to delete
        """
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"Deleted local file: {file_path}")
            except Exception as e:
                logger.warning(f"Could not delete file {file_path}: {e}")

    async def _process_mcq_parsing_batches(self, text_content: str, total_questions: int, resource_id: str,
                                         chapter_id: str, book_id: str, request_id: str, combined_file_path: str, username: str, images_from_content: List[str]) -> Optional[str]:
        """
        Process MCQ parsing in batches and combine results with enhanced memory management.

        Args:
            text_content: Combined text content from all extracted files
            total_questions: Total number of questions
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            request_id: Request ID for logging
            combined_file_path: Path to the combined text file
            username: Username of the user performing the extraction

        Returns:
            Optional[str]: S3 path of the final combined JSON file, or None if failed
        """
        try:
            # Initial memory check before starting batch processing
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            initial_memory = check_memory_limit(memory_limit, critical_check=True)
            logger.info(f"🚀 [REQUEST:{request_id}] Starting MCQ parsing - Initial memory: {initial_memory:.2f}MB")

            # Calculate batch size (10 questions per batch)
            batch_size = 10
            num_batches = math.ceil(total_questions / batch_size)

            logger.info(f"[REQUEST:{request_id}] Processing {total_questions} questions in {num_batches} batches of {batch_size}")

            # Create output directory for JSON files
            json_output_dir = os.path.join(os.path.dirname(combined_file_path), "json_batches")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []

            # Process each batch with memory monitoring
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[REQUEST:{request_id}] Processing batch {batch_num}/{num_batches}: questions {start_question}-{end_question}")

                # Memory monitoring before each batch
                try:
                    monitor_result = self.monitor_memory_during_processing(
                        context=f"before batch {batch_num}/{num_batches}",
                        force_cleanup_threshold=0.8  # Cleanup at 80% of limit
                    )

                    if monitor_result.get('cleanup_performed'):
                        logger.info(f"✅ [REQUEST:{request_id}] Preventive cleanup performed before batch {batch_num}")

                    # Critical memory check
                    check_memory_limit(memory_limit, critical_check=True)

                except MemoryError as me:
                    logger.error(f"🚨 [REQUEST:{request_id}] Memory limit exceeded before batch {batch_num}: {me}")
                    # Perform aggressive cleanup before failing
                    force_garbage_collection(aggressive=True)
                    raise

                # Call LLM for this batch
                batch_json_content = await self._call_llm_for_batch(
                    text_content, start_question, end_question, request_id
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"{resource_id}_{batch_num}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        f.write(batch_json_content)

                    batch_json_files.append(batch_json_path)
                    logger.info(f"[REQUEST:{request_id}] Saved batch {batch_num} JSON to {batch_json_filename}")

                    # Clear the batch content from memory immediately
                    del batch_json_content

                    # Force garbage collection after each batch to prevent memory buildup
                    if batch_num % 2 == 0:  # Every 2 batches
                        collected = force_garbage_collection()
                        logger.info(f"🧹 [REQUEST:{request_id}] Cleanup after batch {batch_num}: collected {collected} objects")

                else:
                    logger.error(f"[REQUEST:{request_id}] Failed to get JSON content for batch {batch_num}")
                    return None

            # Final memory cleanup after all batches
            logger.info(f"[REQUEST:{request_id}] All batches completed, performing cleanup before combining files")
            force_garbage_collection(aggressive=True)
            self.log_memory_status("after all batches")

            # Combine all batch JSON files into one
            combined_json_path = await self._combine_json_files(batch_json_files, resource_id, chapter_id, request_id)

            #ToDO
            # Add images to the combined JSON file

            if not combined_json_path:
                logger.error(f"[REQUEST:{request_id}] Failed to combine JSON files")
                return None

            # Memory cleanup after combining files
            force_garbage_collection()
            self.log_memory_status("after combining JSON files")

            # Upload combined JSON to S3
            s3_json_path = await self._upload_combined_json_to_s3(
                combined_json_path, book_id, chapter_id, resource_id, request_id
            )

            # Memory cleanup after S3 upload
            force_garbage_collection()
            self.log_memory_status("after S3 JSON upload")

            # Process MCQs with external API after JSON is saved
            await self._process_mcqs_with_api(s3_json_path, chapter_id, resource_id, request_id, username)

            # Final cleanup after API processing
            force_garbage_collection()
            self.log_memory_status("after API processing")

            # Clean up batch JSON files
            self._cleanup_local_files(batch_json_files + [combined_json_path])

            # Clear text content from memory
            del text_content
            force_garbage_collection()

            logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully with memory management")
            return s3_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in MCQ parsing batches: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                force_garbage_collection(aggressive=True)
                if 'text_content' in locals():
                    del text_content
            except:
                pass
            return None

    async def _call_llm_for_batch(self, text_content: str, start_question: int, end_question: int, request_id: str) -> Optional[str]:
        """
        Call LLM to parse MCQs for a specific batch with memory management.

        Args:
            text_content: Combined text content
            start_question: Starting question number for this batch
            end_question: Ending question number for this batch
            request_id: Request ID for logging

        Returns:
            Optional[str]: JSON string response from LLM, or None if failed
        """
        try:
            # Memory check before LLM call
            self.log_memory_status(f"before LLM call for batch {start_question}-{end_question}")

            # Get LLM with temperature 0 (default)
            llm = llm_factory.get_llm("openai", "gpt-4.1-mini")

            # Create the prompt using mcq_parser_prompt
            prompt_text = mcq_parser_prompt(start_question, end_question, text_content)

            logger.info(f"[REQUEST:{request_id}] Calling LLM for batch questions {start_question}-{end_question}")

            # Call LLM
            response = llm.invoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            json_content = response.content

            logger.info(f"[REQUEST:{request_id}] LLM response received for batch questions {start_question}-{end_question}")

            # Clear prompt and response objects from memory
            del prompt_text, response

            # Memory check after LLM call
            self.log_memory_status(f"after LLM call for batch {start_question}-{end_question}")

            return json_content

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling LLM for batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                if 'prompt_text' in locals():
                    del prompt_text
                if 'response' in locals():
                    del response
                force_garbage_collection()
            except:
                pass
            return None

    async def _combine_json_files(self, batch_json_files: List[str], resource_id: str, chapter_id: str, request_id: str) -> Optional[str]:
        """
        Combine all batch JSON files into one final JSON file with memory management.

        Args:
            batch_json_files: List of batch JSON file paths
            resource_id: Resource ID
            chapter_id: Chapter ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: Path to the combined JSON file, or None if failed
        """
        try:
            # Memory check before combining files
            self.log_memory_status("before combining JSON files")

            combined_questions = []

            # Read and parse each batch JSON file
            for i, batch_file in enumerate(batch_json_files):
                try:
                    with open(batch_file, "r", encoding="utf-8") as f:
                        batch_content = f.read().strip()

                    # Parse JSON content
                    batch_json = json.loads(batch_content)

                    # Extract questions array
                    if "questions" in batch_json and isinstance(batch_json["questions"], list):
                        combined_questions.extend(batch_json["questions"])
                        logger.info(f"[REQUEST:{request_id}] Added {len(batch_json['questions'])} questions from {os.path.basename(batch_file)}")
                    else:
                        logger.warning(f"[REQUEST:{request_id}] No 'questions' array found in {os.path.basename(batch_file)}")

                    # Clear batch content from memory immediately
                    del batch_content, batch_json

                    # Periodic memory cleanup during file processing
                    if (i + 1) % 5 == 0:  # Every 5 files
                        collected = force_garbage_collection()
                        logger.info(f"🧹 [REQUEST:{request_id}] Cleanup after processing {i+1} JSON files: collected {collected} objects")

                except json.JSONDecodeError as e:
                    logger.error(f"[REQUEST:{request_id}] JSON decode error in {os.path.basename(batch_file)}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error reading {os.path.basename(batch_file)}: {e}")
                    continue

            # Create final combined JSON structure
            final_json = {
                "questions": combined_questions
            }

            # Create combined JSON file path
            combined_json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            combined_json_path = os.path.join(os.path.dirname(batch_json_files[0]), combined_json_filename)

            # Write combined JSON file
            with open(combined_json_path, "w", encoding="utf-8") as f:
                json.dump(final_json, f, indent=2, ensure_ascii=False)

            logger.info(f"[REQUEST:{request_id}] Combined {len(combined_questions)} questions into {combined_json_filename}")

            # Clear large objects from memory
            del combined_questions, final_json
            force_garbage_collection()
            self.log_memory_status("after combining JSON files")

            return combined_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error combining JSON files: {e}")
            logger.error(traceback.format_exc())
            # Cleanup on error
            try:
                if 'combined_questions' in locals():
                    del combined_questions
                if 'final_json' in locals():
                    del final_json
                force_garbage_collection()
            except:
                pass
            return None

    async def _upload_combined_json_to_s3(self, combined_json_path: str, book_id: str, chapter_id: str,
                                        resource_id: str, request_id: str) -> Optional[str]:
        """
        Upload the combined JSON file to S3.

        Args:
            combined_json_path: Path to the combined JSON file
            book_id: Book ID
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: S3 path of the uploaded file, or None if failed
        """
        try:
            # Upload combined JSON file to S3
            json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            s3_upload_result = upload_file_to_s3(
                local_file_path=combined_json_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=json_filename,
                is_quiz_image=False
            )

            if s3_upload_result:
                logger.info(f"[REQUEST:{request_id}] Successfully uploaded combined JSON to S3: {s3_upload_result}")
                return s3_upload_result
            else:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined JSON to S3")
                return None

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error uploading combined JSON to S3: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _process_mcqs_with_api(self, json_s3_path: str, chapter_id: str, resource_id: str,
                                   request_id: str, username: str) -> None:
        """
        Process MCQs with external API using the process_mcqs method from MCQExtractor.

        Args:
            json_s3_path: S3 path to the combined JSON file
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging
            username: Username of the user performing the extraction
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting MCQ processing with external API")

            # Read the combined JSON file from S3
            from utils.s3_utils import read_file_from_s3, get_s3_path

            # Get the full S3 path for the JSON file
            full_s3_path = get_s3_path(json_s3_path)
            logger.info(f"[REQUEST:{request_id}] Reading combined JSON from S3: {full_s3_path}")

            # Read content from S3
            content = read_file_from_s3(full_s3_path)
            if content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read JSON file from S3: {full_s3_path}")
                return

            # Convert bytes to string and parse JSON
            content_str = content.decode("utf-8")
            json_content = json.loads(content_str)
            logger.info(f"[REQUEST:{request_id}] Successfully read and parsed JSON from S3")

            # Extract the questions array
            questions = json_content.get("questions", [])

            if not questions:
                logger.warning(f"[REQUEST:{request_id}] No questions found in combined JSON file")
                return

            logger.info(f"[REQUEST:{request_id}] Found {len(questions)} questions to process")

            # Import and create MCQExtractor instance
            from agents.mcq_extractor import MCQExtractor
            mcq_extractor = MCQExtractor()

            # Call the process_mcqs method
            processed_mcqs = await mcq_extractor.process_mcqs(questions, chapter_id, resource_id, username)

            logger.info(f"[REQUEST:{request_id}] Successfully processed {len(processed_mcqs)} MCQs with external API")

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error processing MCQs with external API: {e}")
            logger.error(traceback.format_exc())

    async def get_extracted_text(self, chapter_id: str, res_id: str) -> Dict:
        """
        Read the extracted MCQ text for a resource using the same pattern as PDF text extractor.

        Args:
            chapter_id: Chapter ID
            res_id: Resource ID

        Returns:
            Dict: Text content or error message
        """
        try:
            # Get a database session for the wscontent schema
            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the resource_dtl table to get resource details
                resource_query = text("""
                    SELECT id, chapter_id, extract_path
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.error(f"Resource with ID {res_id} not found in database")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id_db = resource_row[1]
                extract_path = resource_row[2]

                logger.info(f"Found resource ID: {resource_id}, chapter ID: {chapter_id_db}, extract path: {extract_path}")

                # Query the chapters_mst table to get the book_id
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id_db})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.error(f"Chapter with ID {chapter_id_db} not found in database")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id_db} not found"}

                # Extract book_id
                book_id = chapter_row[0]
                logger.info(f"Found book ID: {book_id}")

                # First try to read directly from the extract_path if it exists
                if extract_path:
                    try:
                        from utils.s3_utils import read_file_from_s3, get_s3_path

                        # Try direct path first
                        direct_s3_path = get_s3_path(extract_path)
                        logger.info(f"Trying to read from direct S3 path: {direct_s3_path}")
                        content = read_file_from_s3(direct_s3_path)

                        if content is not None:
                            # Convert bytes to string
                            content_str = content.decode("utf-8")
                            logger.info(f"Successfully read {len(content_str)} characters from direct S3 path")
                            db_session.close()
                            logger.debug("Database session closed after finding content at direct S3 path")
                            return {"status": "success", "content": content_str, "source": "s3_direct", "s3_path": direct_s3_path}
                        else:
                            logger.warning(f"Could not read from direct S3 path: {direct_s3_path}")
                    except Exception as e:
                        logger.warning(f"Error reading from direct S3 path: {e}")

                # Try to construct the S3 path based on the expected MCQ text extraction structure
                try:
                    from utils.s3_utils import read_file_from_s3, get_s3_path

                    # For MCQ text extraction, files go in extractedImages folder with format: {chapter_id}_{res_id}.txt
                    constructed_s3_path = f"supload/pdfextracts/{book_id}/{chapter_id_db}/{resource_id}/extractedImages/{chapter_id_db}_{resource_id}.txt"
                    full_s3_path = get_s3_path(constructed_s3_path)
                    logger.info(f"Trying constructed MCQ text S3 path: {full_s3_path}")

                    content = read_file_from_s3(full_s3_path)
                    if content is not None:
                        # Convert bytes to string
                        content_str = content.decode("utf-8")
                        logger.info(f"Successfully read {len(content_str)} characters from constructed S3 path")

                        # Update the database with the correct path if it wasn't set
                        if not extract_path:
                            try:
                                from utils.db_utils import update_extract_path
                                # Use the utility function to update the database
                                success = update_extract_path(res_id, constructed_s3_path, CONTENT_SCHEMA)

                                if success:
                                    logger.info(f"Updated database with correct extract_path: {constructed_s3_path}")
                                else:
                                    logger.warning(f"Failed to update database with correct path: {constructed_s3_path}")
                            except Exception as update_err:
                                logger.error(f"Failed to update database with correct path: {update_err}")
                                logger.error(traceback.format_exc())

                        db_session.close()
                        logger.debug("Database session closed after finding content at constructed S3 path")
                        return {"status": "success", "content": content_str, "source": "s3_constructed", "s3_path": full_s3_path}
                    else:
                        logger.warning(f"Could not read from constructed S3 path: {full_s3_path}")
                except Exception as e:
                    logger.warning(f"Error reading from constructed S3 path: {e}")

                # If we've reached here, we couldn't find the file
                logger.error(f"MCQ extracted text file not found for resource ID {res_id}")
                return {"status": "error", "message": f"MCQ extracted text file not found for resource with ID {res_id}"}
            finally:
                # Close the database session if it's still active
                if hasattr(db_session, 'is_active') and db_session.is_active:
                    db_session.close()
                    logger.debug("Database session closed at the end of get_extracted_text")

        except Exception as e:
            logger.error(f"Error reading MCQ extracted text: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def get_quiz_images(self, res_id: str, chapter_id: str, book_id: str, image_urls) -> List[str]:
        from agents.core.extractor import ExtractorAgent
        img_extractor = ExtractorAgent()
        start_idx = 0

        for idx in range(start_idx, len(image_urls)):
            col_img_url = image_urls[idx]
            page_name = col_img_url.split('/')[-1]
            print(page_name)
            page_id = page_name.split('.')[0]

            mcq_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id),
                                                     str(res_id))
            mcq_local_image_paths = [
                os.path.join(mcq_full_img_storage_path, img)
                for img in os.listdir(mcq_full_img_storage_path)
                if img.lower().endswith(f"{page_id}.png")
            ]
            mcq_local_image_paths.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))
            print(mcq_local_image_paths)
            logger.info(f"Extracting mcq images...")
            extracted_img_urls = img_extractor.extract_quiz_images(mcq_local_image_paths, book_id, chapter_id, res_id,
                                                                   "mcq", True)
            print(extracted_img_urls)

        return []


    def natural_sort_key(self, s):
        # Extract the number from the filename using regex
        numbers = re.findall(r'(\d+)', s)
        if numbers:
            return int(numbers[0])
        return s
